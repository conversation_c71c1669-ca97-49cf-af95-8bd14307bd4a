#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agents import QLearningAgent
from gridworld import Gridworld, Environment, PRESET_GRIDS, get_start_point

def trace_single_episode():
    """Trace a single episode step by step"""
    grid = PRESET_GRIDS['bridge']
    start = get_start_point(grid)
    
    game = Gridworld(grid)
    agent = QLearningAgent(game, discount=0.9, learning_rate=0.5, explore_prob=0.8)
    env = Environment(agent, noise=0.0, living_reward=0.0, grid=grid)
    
    print("Bridge Grid:")
    for i, row in enumerate(grid):
        print(f"Row {i}: {row}")
    print(f"Start: {start}")
    
    env.position = start
    print(f"\nTracing episode:")
    print(f"Initial position: {env.position}")
    
    for step in range(20):  # Limit steps to prevent infinite loop
        old_pos = env.position
        
        # Check if current position is terminal
        grid_value = grid[env.position[0]][env.position[1]]
        if isinstance(grid_value, (int, float)):
            print(f"Step {step}: At terminal state {env.position} with value {grid_value}")
            break
        
        # Get action
        action = agent.get_action(env.position)
        print(f"Step {step}: At {env.position}, chose action {action}")
        
        # Take step
        episode_ended = env.iterate(action)
        print(f"  -> Moved to {env.position}, episode_ended={episode_ended}")
        
        if episode_ended:
            print(f"  -> Episode ended!")
            break
        
        if env.position == old_pos:
            print(f"  -> Position didn't change (hit wall or stayed)")

def test_manual_path():
    """Test manually following the optimal path"""
    grid = PRESET_GRIDS['bridge']
    start = get_start_point(grid)
    
    game = Gridworld(grid)
    agent = QLearningAgent(game, discount=0.9, learning_rate=0.5, explore_prob=0.0)
    env = Environment(agent, noise=0.0, living_reward=0.0, grid=grid)
    
    print("\nTesting manual optimal path:")
    print("Path: Start -> Right -> Right -> Right -> Right -> Right (to reach +10)")
    
    env.position = start
    path = [Gridworld.Action.Right] * 5  # Should reach (1,6) with reward +10
    
    for i, action in enumerate(path):
        old_pos = env.position
        print(f"Step {i}: At {env.position}, taking {action}")
        
        # Check what's at current position
        grid_value = grid[env.position[0]][env.position[1]]
        print(f"  Current cell value: {grid_value}")
        
        # Take action manually
        episode_ended = env.iterate(action)
        print(f"  -> Moved to {env.position}, episode_ended={episode_ended}")
        
        if episode_ended:
            final_value = grid[env.position[0]][env.position[1]]
            print(f"  -> Reached terminal state with reward {final_value}")
            break

def analyze_q_learning_issue():
    """Analyze why Q-learning isn't finding the optimal path"""
    grid = PRESET_GRIDS['bridge']
    start = get_start_point(grid)
    
    print("\nAnalyzing Q-learning issue:")
    print("The bridge problem:")
    print("- Start at (1,1)")
    print("- Left leads to (1,0) with reward +1")
    print("- Right path leads to (1,6) with reward +10")
    print("- Up/Down leads to cliff with reward -100")
    
    print("\nThe issue:")
    print("1. With epsilon=0: Agent never explores, finds +1 quickly, never discovers +10")
    print("2. With epsilon>0: Agent explores randomly, but...")
    print("   - If it goes Up/Down first, it gets -100 and learns to avoid exploration")
    print("   - If it goes Left first, it gets +1 and prefers that over risky exploration")
    print("   - The +10 reward requires 5 consecutive Right moves, which is very unlikely with random exploration")
    
    print("\nProbability analysis:")
    print("- With 4 actions available, probability of 5 consecutive Rights = (1/4)^5 = 1/1024 ≈ 0.1%")
    print("- Even with epsilon=0.8, most random actions will hit the cliff or go left")
    print("- The -100 penalty is so large that it dominates the Q-values")
    
    print("\nConclusion:")
    print("This is a classic exploration problem. The optimal path requires a very specific")
    print("sequence of actions that is unlikely to be discovered by random exploration,")
    print("especially when there are severe penalties for wrong moves.")

if __name__ == "__main__":
    trace_single_episode()
    test_manual_path()
    analyze_q_learning_issue()
