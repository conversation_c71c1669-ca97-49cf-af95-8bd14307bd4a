#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agents import QLearningAgent
from gridworld import Gridworld, Environment, PRESET_GRIDS, get_start_point

def analyze_bridge_structure():
    """Analyze the bridge grid structure"""
    grid = PRESET_GRIDS['bridge']
    print("Bridge Grid Structure:")
    for i, row in enumerate(grid):
        print(f"Row {i}: {row}")
    
    start = get_start_point(grid)
    print(f"Start position: {start}")
    
    game = Gridworld(grid)
    print(f"Available actions from start: {game.get_actions(start)}")
    
    return grid, start

def test_single_run(epsilon, learning_rate, episodes=50):
    """Test a single run with given parameters"""
    grid = PRESET_GRIDS['bridge']
    start = get_start_point(grid)
    
    game = Gridworld(grid)
    agent = QLearningAgent(game, discount=0.9, learning_rate=learning_rate, explore_prob=epsilon)
    env = Environment(agent, noise=0.0, living_reward=0.0, grid=grid)
    
    print(f"\nTesting epsilon={epsilon}, learning_rate={learning_rate}")
    print(f"Training for {episodes} episodes...")
    
    # Track episode rewards
    episode_rewards = []
    
    for episode in range(episodes):
        env.position = start
        episode_reward = 0
        steps = 0
        
        while steps < 100:  # Prevent infinite loops
            old_pos = env.position
            if env.iterate():  # Episode ended
                # Get the reward from the terminal state
                grid_value = grid[env.position[0]][env.position[1]]
                if isinstance(grid_value, (int, float)):
                    episode_reward = grid_value
                break
            steps += 1
        
        episode_rewards.append(episode_reward)
        if episode < 10 or episode % 10 == 9:
            print(f"Episode {episode+1}: reward = {episode_reward}")
    
    # Check final policy
    print(f"\nFinal Q-values from start {start}:")
    for action in game.get_actions(start):
        q_val = agent.get_q_value(start, action)
        print(f"  {action}: {q_val:.3f}")
    
    best_action = agent.get_best_policy(start)
    print(f"Best action from start: {best_action}")
    
    # Check if learned optimal policy (should prefer Right to get +10)
    q_right = agent.get_q_value(start, Gridworld.Action.Right)
    q_left = agent.get_q_value(start, Gridworld.Action.Left)
    
    optimal = q_right > q_left
    print(f"Learned optimal policy: {optimal} (Q_right={q_right:.3f} > Q_left={q_left:.3f})")
    
    avg_reward = sum(episode_rewards[-10:]) / 10  # Average of last 10 episodes
    print(f"Average reward (last 10 episodes): {avg_reward:.2f}")
    
    return optimal, avg_reward

if __name__ == "__main__":
    # Analyze structure
    analyze_bridge_structure()
    
    # Test different configurations
    print("\n" + "="*60)
    
    # Test epsilon = 0 (pure exploitation)
    test_single_run(epsilon=0.0, learning_rate=0.5, episodes=50)
    
    print("\n" + "="*60)
    
    # Test with some exploration
    test_single_run(epsilon=0.3, learning_rate=0.5, episodes=50)
    
    print("\n" + "="*60)
    
    # Test with high exploration
    test_single_run(epsilon=0.8, learning_rate=0.5, episodes=50)
