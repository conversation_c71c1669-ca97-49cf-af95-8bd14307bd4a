#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agents import question3

def test_question3():
    """Test the question3 function"""
    result = question3()
    print(f"question3() returned: {result}")
    print(f"Type: {type(result)}")
    
    if result == 'NOT POSSIBLE':
        print("✓ Correctly identified that the problem is not solvable")
    elif isinstance(result, tuple) and len(result) == 2:
        epsilon, learning_rate = result
        print(f"✓ Returned tuple: epsilon={epsilon}, learning_rate={learning_rate}")
    else:
        print("✗ Invalid return format")

if __name__ == "__main__":
    test_question3()
