#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agents import QLearningAgent
from gridworld import Gridworld, Environment, PRESET_GRIDS, get_start_point
import random

def test_bridge_learning(epsilon, learning_rate, num_trials=100, episodes_per_trial=50):
    """Test if the agent can learn optimal policy on bridge grid"""
    
    grid = PRESET_GRIDS['bridge']
    successes = 0
    
    for trial in range(num_trials):
        # Create fresh agent for each trial
        game = Gridworld(grid)
        agent = QLearningAgent(game, discount=0.9, learning_rate=learning_rate, explore_prob=epsilon)
        env = Environment(agent, noise=0.0, living_reward=0.0, grid=grid)
        
        # Train for specified number of episodes
        for episode in range(episodes_per_trial):
            # Reset to start position
            env.position = get_start_point(grid)  # Start position
            
            # Run episode until terminal state
            while True:
                if env.iterate():  # Returns True if episode ended
                    break
        
        # Test if agent learned optimal policy
        # Optimal policy: from start, go right to reach reward 10
        start_state = get_start_point(grid)
        
        # Check if agent prefers going right (towards +10) over left (towards +1)
        # We'll check the Q-values for the key decision points
        
        # At start position, should prefer Right over Left
        q_right = agent.get_q_value(start_state, Gridworld.Action.Right)
        q_left = agent.get_q_value(start_state, Gridworld.Action.Left)
        
        # If Q(s, Right) > Q(s, Left), then agent learned optimal policy
        if q_right > q_left:
            successes += 1
    
    success_rate = successes / num_trials
    return success_rate

def analyze_bridge_problem():
    """Analyze different epsilon and learning rate combinations"""
    
    print("Analyzing Bridge Crossing Problem...")
    print("=" * 50)
    
    # Test epsilon = 0 (pure exploitation)
    print("Testing epsilon = 0 (pure exploitation):")
    success_rate = test_bridge_learning(epsilon=0.0, learning_rate=0.5, num_trials=20)
    print(f"Success rate: {success_rate:.2%}")
    
    # Test various epsilon values with default learning rate
    epsilons = [0.1, 0.3, 0.5, 0.7, 0.9]
    learning_rates = [0.1, 0.3, 0.5, 0.7, 0.9]
    
    print("\nTesting different epsilon values (learning_rate=0.5):")
    for eps in epsilons:
        success_rate = test_bridge_learning(epsilon=eps, learning_rate=0.5, num_trials=20)
        print(f"Epsilon {eps}: {success_rate:.2%}")
    
    print("\nTesting different learning rates (epsilon=0.3):")
    for lr in learning_rates:
        success_rate = test_bridge_learning(epsilon=0.3, learning_rate=lr, num_trials=20)
        print(f"Learning rate {lr}: {success_rate:.2%}")
    
    # Find best combination
    print("\nSearching for optimal combination...")
    best_success_rate = 0
    best_params = None
    
    for eps in [0.1, 0.2, 0.3, 0.4, 0.5]:
        for lr in [0.3, 0.5, 0.7, 0.9]:
            success_rate = test_bridge_learning(epsilon=eps, learning_rate=lr, num_trials=50)
            print(f"Epsilon {eps}, LR {lr}: {success_rate:.2%}")
            
            if success_rate > best_success_rate:
                best_success_rate = success_rate
                best_params = (eps, lr)
    
    print(f"\nBest combination: epsilon={best_params[0]}, learning_rate={best_params[1]}")
    print(f"Success rate: {best_success_rate:.2%}")
    
    return best_params if best_success_rate >= 0.99 else None

if __name__ == "__main__":
    result = analyze_bridge_problem()
    if result:
        print(f"\nFound solution: {result}")
    else:
        print("\nNo solution found with >99% success rate")
