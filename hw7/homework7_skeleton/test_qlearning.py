#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agents import QLearningAgent
from gridworld import Gridworld

def test_qlearning_basic():
    """Test basic Q-learning functionality"""
    
    # Create a simple 2x2 grid
    # S = start, # = wall, numbers = terminal states with rewards
    grid = (
        ('S', -1.0),
        ('#', 10.0)
    )
    
    game = Gridworld(grid)
    agent = QLearningAgent(game, discount=0.9, learning_rate=0.1, explore_prob=0.1)
    
    # Test initial Q-values (should be 0)
    state = (0, 0)  # Start position
    action = Gridworld.Action.Right
    
    print("Testing initial Q-value...")
    q_val = agent.get_q_value(state, action)
    print(f"Initial Q-value for state {state}, action {action}: {q_val}")
    assert q_val == 0.0, f"Expected 0.0, got {q_val}"
    
    # Test get_value for initial state
    print("Testing initial state value...")
    value = agent.get_value(state)
    print(f"Initial value for state {state}: {value}")
    assert value == 0.0, f"Expected 0.0, got {value}"
    
    # Test get_best_policy
    print("Testing initial best policy...")
    policy = agent.get_best_policy(state)
    print(f"Initial best policy for state {state}: {policy}")
    assert policy in game.get_actions(state), f"Policy {policy} not in available actions"
    
    # Test get_action (should return a valid action)
    print("Testing get_action...")
    action = agent.get_action(state)
    print(f"Action chosen for state {state}: {action}")
    assert action in game.get_actions(state), f"Action {action} not in available actions"
    
    # Test update method
    print("Testing update method...")
    next_state = (0, 1)
    reward = -1.0
    agent.update(state, Gridworld.Action.Right, next_state, reward)
    
    # Check that Q-value was updated
    new_q_val = agent.get_q_value(state, Gridworld.Action.Right)
    print(f"Q-value after update: {new_q_val}")
    assert new_q_val != 0.0, f"Q-value should have been updated, but is still {new_q_val}"
    
    print("All basic tests passed!")

def test_qlearning_terminal_state():
    """Test Q-learning with terminal states"""
    
    grid = (
        ('S', 10.0),
        ('#', -10.0)
    )
    
    game = Gridworld(grid)
    agent = QLearningAgent(game, discount=0.9, learning_rate=0.1, explore_prob=0.1)
    
    # Terminal state should have no actions and value 0
    terminal_state = (0, 1)
    actions = game.get_actions(terminal_state)
    print(f"Actions for terminal state {terminal_state}: {actions}")
    assert len(actions) == 0, f"Terminal state should have no actions, got {actions}"
    
    value = agent.get_value(terminal_state)
    print(f"Value of terminal state: {value}")
    assert value == 0.0, f"Terminal state value should be 0.0, got {value}"
    
    policy = agent.get_best_policy(terminal_state)
    print(f"Policy for terminal state: {policy}")
    assert policy is None, f"Terminal state policy should be None, got {policy}"
    
    print("Terminal state tests passed!")

if __name__ == "__main__":
    print("Testing Q-learning implementation...")
    test_qlearning_basic()
    test_qlearning_terminal_state()
    print("All tests passed! Q-learning implementation looks good.")
